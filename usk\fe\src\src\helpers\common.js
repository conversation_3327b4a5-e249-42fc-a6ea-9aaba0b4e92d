import dayjs from 'boot/dayjs';
import DOMPurify from 'dompurify';
import Encoding from 'encoding-japanese';
import linkifyHtml from 'linkify-html';
import papa from 'papaparse';
import {
  ENTERPRISE_TYPE_ENUM,
  ENTERPRISE_TYPE_OPTIONS,
  LIST_PROVINCE,
  OPTIONS_STATUS_REGISTER_ACCOUNT,
  OPTIONS_STATUS_USER,
  OPTION_ENABLE_EXPORT_FUNCTION,
  PARTNER_TYPE_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
  STAFF_OR_ENTERPRISE,
  STAFF_TYPE_ENUM,
  USER_ROLE_OPTIONS,
} from 'src/helpers/constants';
import commonService from 'src/shared/services/common.service';

export function FORMAT_DATE(value, format = 'YYYY/MM/DD') {
  if (value) {
    return dayjs.getDate(value).format(format);
  }
  return '';
}

export function FORMAT_DATE_UTC(value) {
  if (value) {
    return dayjs.tz(value).format('YYYY/MM/DD');
  }
  return '';
}

export function FORMAT_DATE_TIME_UTC(value) {
  if (value) {
    return dayjs.tz(value).format('YYYY/MM/DD HH:mm');
  }
  return '';
}

export function FORMAT_DATE_JAPAN(value) {
  if (value) {
    return dayjs.getDate(value).format('YYYY年M月D日');
  }
  return dayjs.getDate().format('YYYY年M月D日');
}

export function FORMAT_DATE_NOW(format = 'YYYY-MM-DD') {
  return dayjs.getDate().format(format);
}

export function FORMAT_DATE_YESTERDAY() {
  return dayjs.getDate().subtract(1, 'days').format('YYYY-MM-DD');
}

export function FORMAT_DATE_TOMORROW() {
  return dayjs.getDate().add(1, 'days').format('YYYY-MM-DD');
}

export function GET_BETWEEN_DAYS_FROM_NOW(day) {
  return dayjs(FORMAT_DATE(day)).diff(FORMAT_DATE_NOW(), 'day');
}

export function GET_DATE_FROM_DAY(day) {
  return dayjs.tz(day).get('date');
}

export function GET_MONTH_FROM_DAY(day) {
  return dayjs.tz(day).get('month') + 1;
}

export function GET_DAY_WEEK_FROM_DAY(day) {
  const dayOfWeek = dayjs.tz(day).get('day');
  switch (dayOfWeek) {
    case 0:
      return '日';
    case 1:
      return '月';
    case 2:
      return '火';
    case 3:
      return '水';
    case 4:
      return '木';
    case 5:
      return '金';
    case 6:
      return '土';
    default:
      return '';
  }
}

export function GET_BETWEEN_DAYS(day1, day2) {
  return dayjs(FORMAT_DATE(day1)).diff(FORMAT_DATE(day2), 'day');
}

export function FORMAT_YEAR_NOW() {
  return dayjs.getDate().format('YYYY');
}

export function FORMAT_MONTH_NOW() {
  return dayjs.getDate().format('MM');
}

export function FORMAT_DAY_NOW() {
  return dayjs.getDate().format('DD');
}

export function LATEST_DAY_OF_MONTH(day) {
  return dayjs.getDate(day).endOf('month').format('D');
}

export function FORMAT_DATE_NOW_PLUS() {
  const hour = dayjs.getDate().get('hour');
  if (hour >= 0 && hour <= 14) {
    return dayjs.getDate().format('YYYY-MM-DD');
  }
  if (hour >= 15 && hour <= 23) {
    return dayjs.getDate().add(1, 'days').format('YYYY-MM-DD');
  }
}

export function COMPARE_FULL_DATE_TIME_WITH_NOW(date1, isLatestDay = false) {
  const format = !isLatestDay ? 'YYYYMMDDHHmmss' : 'YYYYMMDD235959';
  const d1 = dayjs.getDate(date1).format(format);
  const d2 = dayjs.getDate().format('YYYYMMDDHHmmss');
  if (d1 === d2) { return 0; }
  if (d1 < d2) { return -1; }
  return 1;
}

export function FORMAT_TIME(value) {
  if (value) {
    return dayjs.getDate(value).format('HH:mm:ss');
  }
  return '';
}

export function FORMAT_NUMBER(value, numberToFix = 0) {
  return value !== null && value !== undefined
    ? Intl.NumberFormat('ja-JP', {
        minimumFractionDigits: numberToFix,
        maximumFractionDigits: 2,
      }).format((+value).toFixed(2))
    : '';
}

export function FORMAT_LICENSE_NUMBER(value) {
  if (value === null || value === undefined) { return ''; }

  const clean = String(value).replace(/\D/g, '');

  if (clean.length !== 7) { return value; }

  return clean.replace(/^(\d{3})(\d{4})$/, '$1-$2');
}

export function FORMAT_ID_NUMBER(value) {
  if (value === null || value === undefined) { return ''; }

  const clean = String(value).replace(/\D/g, '');

  if (clean.length !== 11) { return value; }

  return clean.replace(/^(\d{7})(\d{4})$/, '$1-$2');
}

export function FORMAT_NUMBER_WEIGHT(value, numberToFix = 2) {
  if (value === null || value === undefined || value === '') { return ''; }

  const num = Number(value);
  if (num === 0) {
    return '000,000.00';
  }

  const fixed = num.toFixed(numberToFix);
  const [intPart, decimalPart] = fixed.split('.');

  const formattedInt = Intl.NumberFormat('en-US').format(Number(intPart));

  return `${formattedInt}.${decimalPart}`; // "1,234.50"
}
export function FORMAT_NUMBER_WITH_COMMA_AND_HALFSPACE(value) {
  if (value === null || value === undefined || value === '') {
    return '';
  }

  const num = Number(value);
  if (Number.isNaN(num)) {
    return '';
  }

  // Truncate to 2 decimal places without rounding
  const truncated = Math.floor(num * 100) / 100;

  // Split into integer and decimal parts
  const parts = truncated.toString().split('.');
  const integerPart = parts[0];
  const decimalPart = parts[1] || '';

  // Add comma separator for every 3 digits
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Ensure 2 decimal places
  const formattedDecimal = decimalPart.padEnd(2, '0').substring(0, 2);

  // Add halfspace character (U+2009)
  const halfSpace = '\u2009';

  return `${formattedInteger}.${formattedDecimal}${halfSpace}`;
}

export function FORMAT_DATE_TIME_CSV(format = 'YYYYMMDD_HHmm') {
  return dayjs.getDate().format(format);
}

export function FORMAT_TEXT_JAPAN(value, arrTextJapan) {
  return arrTextJapan[value - 1];
}

// TODO: Remove this after refactoring
// this function will be used to generate code suffix
export function GENERATE_IDENTITY_CODE() {
  const randomNumber = Math.floor(Math.random() * 1000);
  return randomNumber.toString().padStart(3, '0');
}

/**
 * Generate code suffix for proxy outbound shipment registration.
 * @param {string} codeGroupKey - The key for the code group. (apply code + date)
 * @returns {Promise<string|null>} - Returns the code suffix or null if the request fails
 */
export async function GENERATE_CODE_SUFFIX(codeGroupKey) {
  const response = await commonService.getCodeSuffix({
    code_group_key: codeGroupKey,
  });
  if (response.code === 0) {
    return response.payload;
  }

  return null;
}

export function CONVERT_TEXT_UNIT_TYPE_SETTING_ENUM(value) {
  let text = '';
  switch (value) {
    case 1:
      text = '重量のみ';
      break;
    case 2:
      text = '尾数のみ';
      break;
    case 3:
      text = '両方';
      break;
    default:
      break;
  }
  return text;
}

export function CONVERT_TEXT_SHOW_SHIPPING_DESTINATION(value) {
  if (value) {
    return '表示';
  }
  return '非表示';
}

export function CONVERT_TEXT_SHOW_IMPORT_TODAY(value) {
  if (value) {
    return '表示';
  }
  return '非表示';
}

export function CONVERT_TEXT_EXPORT(value) {
  if (value) {
    return '表示';
  }
  return '非表示';
}

export function CONVERT_TEXT_SHOW_DEFAULT_SCAN_QR(value) {
  let text = '';
  switch (value) {
    case 1:
      text = 'カメラ';
      break;
    case 2:
      text = 'QRスキャナー';
      break;
    case 3:
      text = 'ユーザーID';
      break;
    default:
      break;
  }
  return text;
}

export function CHECK_ROLE(roles = [], type = [], staff = [], user = {}) {
  if (roles.length === 0) {
    return true;
  }

  if (!user) {
    return false;
  }

  if (!roles.includes(user.role)) {
    return false;
  }

  if (type.length && !type.includes(user.enterprise_type)) {
    return false;
  }

  if (staff.length && !staff.includes(user.staff_type)) {
    return false;
  }
  return true;
}

export function maskCodeString(str) {
  if (str?.length !== 16) {
    return '';
  }
  const part1 = str.slice(0, 7);
  const part2 = str.slice(7, 13);
  const part3 = str.slice(13, 16);
  return `${part1}-${part2}-${part3}`;
}

export function exportCSV(data, keyDataColumns, headerNameFields, fileName) {
  const csvVal = papa.unparse(data, {
    header: false,
    columns: keyDataColumns,
  });
  const headersVal = papa.unparse({
    fields: headerNameFields,
    data: [],
  });
  const csv = headersVal + csvVal;
  const convertToShiftJIS = target => {
    const uniArray = Encoding.stringToCode('\ufeff'.concat(target));
    const sjisArray = Encoding.convert(uniArray, {
      to: 'UTF8',
      from: 'UNICODE',
      bom: true,
    });
    const unit8Array = new Uint8Array(sjisArray);
    return unit8Array;
  };
  const csvData = new Blob([convertToShiftJIS(csv)], { type: 'text/csv;charset=UTF-8-BOM' });
  let csvURL = null;
  if (window.navigator.msSaveBlob) {
    csvURL = window.navigator.msSaveBlob(csvData, fileName);
  } else {
    csvURL = window.URL.createObjectURL(csvData);
  }
  const tempLink = document.createElement('a');
  tempLink.href = csvURL;
  tempLink.setAttribute('charset', 'UTF-8-BOM');
  tempLink.setAttribute('download', fileName);
  tempLink.style.visibility = 'hidden';
  tempLink.click();
  tempLink.remove();
}

export function linkify(inputText) {
  const options = {
    target: '_blank',
    rel: 'noopener noreferrer',
    defaultProtocol: 'https',
    className: 'tw:underline-offset-1 tw:cursor-pointer tw:break-all',
  };
  const encodedStr = inputText.replace(
    /[\u00A0-\u9999<>&]/g,
    i => `&#${i.charCodeAt(0)};`
  );
  const result = linkifyHtml(
    encodedStr
      .replace(/\t| {4}| {3}/g, '&emsp;')
      .replace(/\r?\n/g, '<br />'),
    options
  );
  return result;
}

export function clearHTML(inputText) {
  const clearText = DOMPurify.sanitize(inputText);
  return clearText;
}

export function CHECK_EXPIRY_DATE_LICENSE(expiryDate, day = 14) {
  if (!expiryDate) { return false; }
  return dayjs().subtract(+day, 'day').diff(expiryDate, 'day') <= 0;
}

export function doParseFloatNumber(numString) {
  if (typeof numString === 'string') {
    const output = parseFloat(numString.replace(/,/g, ''));
    return Number.isNaN(output) ? '' : output;
  }
  return numString;
}

export function isNumeric(str) {
  if (!str) { return true; }
  if (typeof str !== 'string') { return false; }
  return !Number.isNaN(str)
    && !Number.isNaN(parseFloat(str));
}

export function showProvince(provinceId) {
  let provinceName = '';
  const findProvince = LIST_PROVINCE.find(item => item.value === provinceId);
  if (findProvince) { provinceName = findProvince.label; }
  return provinceName;
}

export function showStatus(status) {
  let statusName = '';
  const findStatus = OPTIONS_STATUS_USER.find(item => item.value === status);
  if (findStatus) { statusName = findStatus.label; }
  return statusName;
}

export function showRole(user = {}) {
  let value = ROLES_ENUM_OPTIONS_VALUES.EEL_FARMING_ENTERPRISE;
  let roleName = '';
  if (user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
    if (user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
      value = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_ENTERPRISE;
    } else {
      value = ROLES_ENUM_OPTIONS_VALUES.DISTRIBUTE_STAFF;
    }
  }
  if (user.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    if (user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE) {
      value = ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE;
    } else {
      value = ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF;
    }
  }
  const findRole = USER_ROLE_OPTIONS.find(item => item.value === value);
  if (findRole) { roleName = findRole.label; }
  return roleName;
}

export function showLicenseNumber(licenseNumber, expiryDate, enterprise_type) {
  let licenseNumberName = '';
  if (enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE || enterprise_type === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    const dateTimeNow = dayjs().format('YYYY/MM/DD');
    const dateTimeExpiry = dayjs(expiryDate).format('YYYY/MM/DD');
    if (dateTimeNow < dateTimeExpiry) {
      licenseNumberName = licenseNumber;
    } else {
      licenseNumberName = '採捕許可なし';
    }
  }
  return licenseNumberName;
}

export function showTypeStaffOrEnterprise(showType) {
  let provinceName = '';
  const findProvince = STAFF_OR_ENTERPRISE.find(item => item.value === showType);
  if (findProvince) { provinceName = findProvince.label; }
  return provinceName;
}

const makeRequest = (method, url, file) => new Promise((resolve, reject) => {
  const xhr = new XMLHttpRequest();
  xhr.open(method, url);
  xhr.onload = () => {
    if (xhr.status >= 200 && xhr.status < 300) {
      resolve({
        status: xhr.status,
        response: xhr.response,
        headers: xhr.getAllResponseHeaders(),
        etag: xhr.getResponseHeader('ETag'),
      });
    } else {
      reject({
        status: xhr.status,
        statusText: xhr.statusText,
      });
    }
  };
  xhr.onerror = () => {
    reject({
      status: xhr.status,
      statusText: xhr.statusText,
    });
  };
  xhr.send(file);
});

export async function uploadFile2S3(signedUrl, file) {
  await makeRequest('PUT', signedUrl, file);
}

export function calcDisabledRadioButtonPartnerType(userRole, partnerRole) {
  const result = {
    [PARTNER_TYPE_ENUM.SUPPLIER.toString()]: true,
    [PARTNER_TYPE_ENUM.SHIPPER.toString()]: true,
  };

  if (userRole === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE) {
    result[PARTNER_TYPE_ENUM.SHIPPER.toString()] = false;
    return result;
  }

  if (userRole === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
    switch (partnerRole) {
      case ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE:
        result[PARTNER_TYPE_ENUM.SUPPLIER.toString()] = false;
        break;
      case ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE:
        result[PARTNER_TYPE_ENUM.SHIPPER.toString()] = false;
        result[PARTNER_TYPE_ENUM.SUPPLIER.toString()] = false;
        break;
      case ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE:
        result[PARTNER_TYPE_ENUM.SUPPLIER.toString()] = false;
        break;
      default:
        break;
    }

    return result;
  }

  if (userRole === ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE) {
    result[PARTNER_TYPE_ENUM.SUPPLIER.toString()] = false;
    return result;
  }

  return result;
}

export function showEnterpriseType(enterpriseType) {
  let enterpriseTypeName = '';
  const findEnterpriseType = ENTERPRISE_TYPE_OPTIONS.find(item => item.value === +enterpriseType);
  if (findEnterpriseType) { enterpriseTypeName = findEnterpriseType.label; }
  return enterpriseTypeName;
}

export function showStatusRegister(status) {
  let statusName = '';
  const findStatus = OPTIONS_STATUS_REGISTER_ACCOUNT.find(item => item.value === status);
  if (findStatus) { statusName = findStatus.label; }
  return statusName;
}

export function showEnableExport(status) {
  let statusName = '';
  const findStatus = OPTION_ENABLE_EXPORT_FUNCTION.find(item => item.value === status);
  if (findStatus) { statusName = findStatus.label; }
  return statusName;
}
